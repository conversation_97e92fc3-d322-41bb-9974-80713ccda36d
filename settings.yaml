# settings.yaml - FMDLES 最佳参数配置 for pyfmto framework
results: out/results

launcher:
  algorithms: [FMDLES]
  problems: [arxiv2017]
  repeat: 20
  save_res: True
  clean_tmp: True

problems:
  arxiv2017:
    dim: 10
    fe_max: 110
    fe_init: 21
    np_per_dim: [0,2,4,6]  

# 算法参数配置
algorithms:
  FMDLES:
    client:
      exploration_ratio: 0.7 # 控制评估次数的分配比例
      exploration_iterations: 20
      learning_rate: 0.3
      epochs: 6
    server:
      # 最佳实验参数配置
      similarity_threshold: 0.3  # 最佳相似性阈值
      top_k_similar: 6  # 最佳top_k_similar值
      use_personalized_aggregation: 1

  # IAFFBO算法配置（CPU优化版本）
  IAFFBO:
    client:
      # 算法核心参数（匹配MATLAB默认值）
      acq_type: LCB              # 获取函数类型：LCB/UCB/EI
      phi: 0.1                   # CSO学习参数
      max_iter: 100              # CSO最大迭代次数
      privacy_noise: 0.0         # 隐私噪声比例

    server:
      n_clusters: 6              # 聚类数量（匹配MATLAB）
      n_samples: 100             # x_hat采样数量
      agg_interval: 0.3          # 聚合间隔

      # GPU服务器优化
      device: cuda               # 服务器设备选择
      batch_aggregation: True    # 批量聚合优化

reporter:
  algorithms:
    - [FDEMD,FMTBO,IAFFBO,FMDLES]
  problems: [arxiv2017]
  np_per_dim: [2]  
