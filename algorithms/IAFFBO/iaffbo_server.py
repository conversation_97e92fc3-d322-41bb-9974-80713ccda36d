from pyDOE import lhs
from pyfmto.framework import Server, ClientPackage, ServerPackage
from pyfmto.utilities import logger
from .iaffbo_utils import (
    Actions,
    compute_task_similarity_matlab,
    model_agg_matlab
)
from .addfbo_utils import ClientsData


class IAFFBOServer(Server):
    """
    n_clusters: 6
    n_samples: 100
    agg_interval: 0.3
    """
    def __init__(self, **kwargs):
        super().__init__()
        kwargs = self.update_kwargs(kwargs)
        self.n_clusters = kwargs['n_clusters']
        self.n_samples = kwargs['n_samples']
        self.x_hat = None
        self.set_agg_interval(kwargs.get('agg_interval'))
        self.clients_data = ClientsData()

    def handle_request(self, client_data: ClientPackage) -> ServerPackage:
        if client_data.action == Actions.PUSH_WEIGHTS:
            return self._handle_push_weights(client_data)
        elif client_data.action == Actions.PULL_WEIGHTS:
            return self._handle_pull_weights(client_data)
        elif client_data.action == Actions.PULL_INIT:
            return self._handle_pull_init(client_data)
        else:
            logger.warning(f"Unknown action: {client_data.action}")
            return ServerPackage('error', {'status': 'unknown_action'})

    def _handle_push_weights(self, client_data: ClientPackage) -> ServerPackage:
        client_id = client_data.cid
        data = client_data.data
        self.clients_data.update_src(cid=client_id, version=data['version'], data=data['weights'])
        logger.debug(f"Client {client_id} pushed weights with version {data['version']}")
        return ServerPackage('success', {'status': 'weights_received'})

    def _handle_pull_weights(self, client_data: ClientPackage) -> ServerPackage:
        client_id = client_data.cid
        version = client_data.data['version']
        clt_res = self.clients_data.get_res(client_id)

        logger.debug(f"Client {client_id} requesting weights: client_ver={version}, server_res_ver={clt_res.version}")

        # If client has newer version than server result, return no data (wait for aggregation)
        if version > clt_res.version:
            logger.debug(f"Client {client_id} version {version} > server result version {clt_res.version}, returning no data")
            return ServerPackage('data', None)
        # If versions match or client is behind, return the aggregated weights
        else:
            if clt_res.data is not None:
                logger.debug(f"Client {client_id} receiving aggregated weights with version {clt_res.version}")
                return ServerPackage('success', {'weights': clt_res.data, 'x_hat': self.x_hat})
            else:
                logger.debug(f"Client {client_id} no aggregated weights available yet")
                return ServerPackage('data', None)

    def _handle_pull_init(self, client_data: ClientPackage) -> ServerPackage:
        self.dim = client_data.data['dim']
        if self.x_hat is None:
            self.gen_x_hat()
        pkg = ServerPackage('success', self.x_hat)
        return pkg

    def lts_res_ver(self, cid: int):
        return self.clients_data.get_res(cid).version

    @property
    def lts_sync_src_ver(self):
        min_ver, len_ver = self.clients_data.available_src_ver
        # MATLAB implementation aggregates when we have data from all active clients
        # But we allow some flexibility for robustness
        min_required_clients = max(1, min(self.num_clients, len_ver))
        res = min_ver if len_ver >= min_required_clients and min_ver >= 0 else -1
        logger.debug(f"Version sync check: min_ver={min_ver}, clients_with_data={len_ver}/{self.num_clients}, sync_ver={res}")
        return res

    def aggregate(self, client_id: int):
        """
        Aggregate client weights exactly matching MATLAB implementation

        MATLAB code:
        % Clustering the model weights
        weightvector = [];
        for ff = 1:client_num
            weightvector(ff,:) = ConvertModelParaToVector(netsum,ff);
        end
        [associate,~]=kmeans(weightvector,cl_num);

        % Then in CSO phase:
        CID = find(associate==associate(ff));
        net_agg = model_agg(netsum,CID');
        """
        src_ver = self.lts_sync_src_ver
        res_ver = self.lts_res_ver(client_id)

        logger.debug(f"Aggregating for client {client_id}: src_ver={src_ver}, res_ver={res_ver}")

        if src_ver >= 0 and res_ver < src_ver:
            try:
                client_weights_list = self.get_clients_data(src_ver)

                # Filter out None values (clients that haven't submitted this version yet)
                valid_weights = [w for w in client_weights_list if w is not None]
                valid_client_ids = [cid for cid, w in zip(self.sorted_ids, client_weights_list) if w is not None]

                if len(valid_weights) == 0:
                    logger.warning(f"No valid weights available for aggregation at version {src_ver}")
                    return

                logger.info(f"Aggregating {len(valid_weights)} client weights for version {src_ver}")

                # MATLAB: [associate,~]=kmeans(weightvector,cl_num);
                associate = compute_task_similarity_matlab(valid_weights, self.n_clusters)
                logger.debug(f"Task similarity clustering result: {associate}")

                # For each client that has valid data, find similar clients and aggregate
                for idx, client_weights in enumerate(valid_weights):
                    actual_client_id = valid_client_ids[idx]

                    # MATLAB: CID = find(associate==associate(ff));
                    # Find all clients in the same cluster as current client
                    # Note: associate uses 1-based indexing from MATLAB, convert to 0-based
                    current_cluster = associate[idx]
                    similar_client_indices = [i for i, cluster in enumerate(associate) if cluster == current_cluster]

                    logger.debug(f"Client {actual_client_id} in cluster {current_cluster}, similar clients: {[valid_client_ids[i] for i in similar_client_indices]}")

                    # MATLAB: net_agg = model_agg(netsum,CID');
                    # Aggregate weights from similar clients
                    aggregated_weights = model_agg_matlab(valid_weights, similar_client_indices)

                    # Update result for this client
                    self.clients_data.update_res(actual_client_id, src_ver, aggregated_weights)
                    logger.debug(f"Updated aggregated weights for client {actual_client_id} with version {src_ver}")

                self.gen_x_hat()
                logger.info(f"Aggregation completed for version {src_ver}")

            except Exception as e:
                logger.error(f"Error during aggregation: {e}")
                import traceback
                logger.error(traceback.format_exc())
        else:
            logger.debug(f"Skipping aggregation for client {client_id}: src_ver={src_ver}, res_ver={res_ver}")

    def gen_x_hat(self):
        self.x_hat = lhs(self.dim, self.n_samples)

    def get_clients_data(self, src_ver):
        return [self.clients_data.get_src(cid, src_ver).data for cid in self.sorted_ids]
