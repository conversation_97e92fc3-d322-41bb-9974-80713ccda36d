#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联邦多任务优化系统 - 基于DLES算法
客户端-服务器架构，保护数据隐私
"""

import numpy as np
import torch
import torch.nn as nn
import copy
from typing import List, Dict, Tuple
from scipy.stats import pearsonr
from dles_algorithm import DLESNetwork, DLES
from Tasks.benchmark import create_tasks_diff_func


class FederatedDLESClient:
    """
    联邦DLES客户端
    每个客户端负责优化一个特定任务
    """
    
    def __init__(self, client_id: int, task, task_name: str, dimension: int = 10,
                 max_evaluations: int = 110, iid: bool = False, np_per_dim: int = 2):
        """
        初始化客户端
        Args:
            client_id: 客户端ID
            task: 优化任务
            task_name: 任务名称
            dimension: 问题维度
            max_evaluations: 最大评估次数
            iid: 是否使用IID数据划分
            np_per_dim: 每个特征维度的划分数量
        """
        self.client_id = client_id
        self.task = task
        self.task_name = task_name
        self.dimension = dimension
        self.max_evaluations = max_evaluations
        self.iid = iid
        self.np_per_dim = np_per_dim

        # 数据划分边界
        self.partition_bounds = self._compute_partition_bounds()

        # 评估次数分配：70%用于探索，30%用于联邦开发
        self.exploration_evaluations = int(max_evaluations * 0.7)
        self.exploitation_evaluations = max_evaluations - self.exploration_evaluations

        # 本地DLES算法实例
        self.local_dles = DLES(
            objective_function=task,
            dimension=dimension,
            population_size=10,
            max_evaluations=self.exploration_evaluations,  # 只用70%进行探索
            exploration_iterations=20,
            learning_rate=0.3,
            epochs=3
        )

        # 本地最优解
        self.best_individual = None
        self.best_fitness = float('inf')
        self.local_dataset = []

        # 联邦学习相关
        self.global_model = None
        self.local_model = DLESNetwork(dimension)

        # 记录每次评估的适应度值
        self.fitness_values = []

        # 记录优化轨迹用于相似性计算
        self.trajectory_data = {
            'best_fitness_sequence': [],  # 最佳适应度序列
            'improvement_rates': [],      # 改进率序列
            'evaluation_points': []       # 评估点序列
        }

    def _compute_partition_bounds(self):
        """计算数据划分边界"""
        if self.iid:
            # IID情况下，所有客户端使用相同的搜索空间
            return None
        else:
            # Non-IID情况下，根据客户端ID和np_per_dim划分搜索空间
            if hasattr(self.task, 'x_lb') and hasattr(self.task, 'x_ub'):
                x_lb = self.task.x_lb
                x_ub = self.task.x_ub

                if isinstance(x_lb, (int, float)):
                    x_lb = np.full(self.dimension, x_lb)
                    x_ub = np.full(self.dimension, x_ub)

                # 计算每个维度的划分
                bounds = []
                for d in range(self.dimension):
                    dim_range = x_ub[d] - x_lb[d]
                    partition_size = dim_range / self.np_per_dim
                    partition_id = self.client_id % self.np_per_dim

                    lower = x_lb[d] + partition_id * partition_size
                    upper = x_lb[d] + (partition_id + 1) * partition_size
                    bounds.append([lower, upper])

                return np.array(bounds)
            else:
                # 默认边界 [-100, 100]
                bounds = []
                for d in range(self.dimension):
                    partition_size = 200.0 / self.np_per_dim  # 总范围200
                    partition_id = self.client_id % self.np_per_dim

                    lower = -100.0 + partition_id * partition_size
                    upper = -100.0 + (partition_id + 1) * partition_size
                    bounds.append([lower, upper])

                return np.array(bounds)
        
    def local_exploration(self) -> Dict:
        """
        执行本地探索阶段
        Returns:
            本地探索结果（不包含原始数据）
        """
        print(f"客户端 {self.client_id} 开始本地探索任务: {self.task_name}")

        # 如果使用Non-IID划分，修改DLES的初始化范围
        if not self.iid and self.partition_bounds is not None:
            self._modify_dles_bounds()

        # 重写evaluate方法以记录适应度值
        original_evaluate = self.local_dles.evaluate

        def evaluate_with_recording(individuals):
            fitness_values = original_evaluate(individuals)
            # 记录每次评估的适应度值
            if hasattr(fitness_values, '__len__'):
                self.fitness_values.extend(fitness_values.tolist())
            else:
                self.fitness_values.append(fitness_values)

            # 记录轨迹数据
            self._update_trajectory_data()
            return fitness_values

        self.local_dles.evaluate = evaluate_with_recording

        # 执行本地探索
        self.local_dles.exploration_sampling()

        # 更新本地最优解
        self.best_individual = self.local_dles.best_individual.copy()
        self.best_fitness = self.local_dles.best_fitness
        self.local_dataset = self.local_dles.dataset.copy()

        # 返回聚合信息（不包含原始数据）
        return {
            'client_id': self.client_id,
            'task_name': self.task_name,
            'best_fitness': self.best_fitness,
            'dataset_size': len(self.local_dataset),
            'evaluations_used': self.local_dles.evaluations,
            'fitness_statistics': self._compute_fitness_statistics(),
            'partition_info': 'IID' if self.iid else f'Non-IID-{self.np_per_dim}',
            'trajectory_data': self.trajectory_data.copy()  # 包含轨迹数据用于相似性计算
        }

    def _modify_dles_bounds(self):
        """修改DLES算法的搜索边界以支持Non-IID划分"""
        # 这里可以修改DLES内部的搜索范围
        # 由于DLES使用objective_function的边界，我们需要在初始化时处理
        pass

    def _update_trajectory_data(self):
        """更新优化轨迹数据"""
        if not self.fitness_values:
            return

        current_best = min(self.fitness_values)
        self.trajectory_data['best_fitness_sequence'].append(current_best)
        self.trajectory_data['evaluation_points'].append(len(self.fitness_values))

        # 计算改进率
        if len(self.trajectory_data['best_fitness_sequence']) > 1:
            prev_best = self.trajectory_data['best_fitness_sequence'][-2]
            if prev_best != 0:
                improvement_rate = (prev_best - current_best) / abs(prev_best)
            else:
                improvement_rate = 0.0
            self.trajectory_data['improvement_rates'].append(improvement_rate)
        else:
            self.trajectory_data['improvement_rates'].append(0.0)
    
    def _compute_fitness_statistics(self) -> Dict:
        """计算适应度统计信息（隐私保护）"""
        if not self.local_dataset:
            return {}
        
        fitness_values = [item[1] for item in self.local_dataset]
        return {
            'min_fitness': min(fitness_values),
            'max_fitness': max(fitness_values),
            'mean_fitness': np.mean(fitness_values),
            'std_fitness': np.std(fitness_values),
            'count': len(fitness_values)
        }
    
    def train_local_model(self) -> Dict:
        """
        训练本地模型
        Returns:
            本地模型参数（用于联邦聚合）
        """
        if len(self.local_dataset) < 2:
            print(f"客户端 {self.client_id} 数据集太小，跳过本地训练")
            return None
        
        # 准备训练数据
        individuals = np.array([item[0] for item in self.local_dataset])
        fitness_values = np.array([item[1] for item in self.local_dataset])
        
        X_fitness = torch.FloatTensor(fitness_values).reshape(-1, 1)
        y_individuals = torch.FloatTensor(individuals)
        
        # 训练本地模型
        optimizer = torch.optim.Adam(self.local_model.parameters(), lr=0.3)
        
        for epoch in range(5):  # 本地训练轮数
            optimizer.zero_grad()
            predictions = self.local_model(X_fitness)
            
            # 使用MSE损失（简化版本）
            loss = torch.mean((predictions - y_individuals)**2)
            loss.backward()
            optimizer.step()
        
        print(f"客户端 {self.client_id} 本地模型训练完成，损失: {loss.item():.6f}")
        
        # 返回模型参数
        return {
            'client_id': self.client_id,
            'model_state_dict': copy.deepcopy(self.local_model.state_dict()),
            'dataset_size': len(self.local_dataset)
        }
    
    def update_global_model(self, global_model_state_dict: Dict):
        """
        更新全局模型（支持个性化模型）
        Args:
            global_model_state_dict: 全局模型参数或个性化模型参数
        """
        self.global_model = DLESNetwork(self.dimension)
        self.global_model.load_state_dict(global_model_state_dict)
        print(f"客户端 {self.client_id} 已更新全局模型")
    
    def cma_es_optimization(self, initial_solution, max_iterations=10):
        """
        简化的CMA-ES优化 (适配联邦环境)
        Args:
            initial_solution: 初始解
            max_iterations: 最大迭代次数
        Returns:
            optimized_solution: 优化后的解
            evaluations_used: 使用的评估次数
        """
        current_solution = initial_solution.copy()
        current_fitness_array = self.local_dles.evaluate(current_solution)

        if len(current_fitness_array) == 0:
            return initial_solution, 0

        current_fitness = current_fitness_array[0]
        evaluations_used = 1

        # 简化的CMA-ES参数
        sigma = 0.5  # 步长
        population_size = min(4, 2 + int(2 * np.log(self.dimension)))  # 减少种群大小

        for iteration in range(max_iterations):
            if self.local_dles.evaluations >= self.local_dles.max_evaluations:
                break

            # 生成候选解
            candidates = []
            for _ in range(population_size):
                if self.local_dles.evaluations >= self.local_dles.max_evaluations:
                    break
                candidate = current_solution + sigma * np.random.randn(self.dimension)

                # 边界处理
                if hasattr(self.task, 'x_bound'):
                    bounds = self.task.x_bound
                    if isinstance(bounds, list) and len(bounds) == 2:
                        candidate = np.clip(candidate, bounds[0], bounds[1])
                    else:
                        candidate = np.clip(candidate, bounds[:, 0], bounds[:, 1])
                else:
                    candidate = np.clip(candidate, -100, 100)

                candidates.append(candidate)

            if not candidates:
                break

            # 评估候选解
            candidates = np.array(candidates)
            fitness_values = self.local_dles.evaluate(candidates)

            if len(fitness_values) == 0:
                break

            evaluations_used += len(fitness_values)

            # 选择最佳解
            best_idx = np.argmin(fitness_values)
            best_candidate = candidates[best_idx]
            best_fitness = fitness_values[best_idx]

            # 更新当前解
            if best_fitness < current_fitness:
                current_solution = best_candidate.copy()
                current_fitness = best_fitness
                sigma *= 1.1  # 增加步长
            else:
                sigma *= 0.9  # 减少步长

            # 步长下界
            if sigma < 1e-8:
                break

        return current_solution, evaluations_used

    def univariate_sampling(self, initial_solution, samples_per_dim=5):
        """
        单变量采样优化 (适配联邦环境)
        Args:
            initial_solution: 初始解
            samples_per_dim: 每个维度的采样数
        Returns:
            optimized_solution: 优化后的解
        """
        current_solution = initial_solution.copy()
        current_fitness_array = self.local_dles.evaluate(current_solution)

        if len(current_fitness_array) == 0:
            return initial_solution

        current_fitness = current_fitness_array[0]

        # 对每个维度进行优化
        for dim in range(self.dimension):
            if self.local_dles.evaluations >= self.local_dles.max_evaluations:
                break

            # 获取当前维度的边界
            if hasattr(self.task, 'x_bound'):
                bounds = self.task.x_bound
                if isinstance(bounds, list) and len(bounds) == 2:
                    lower_bound, upper_bound = bounds[0], bounds[1]
                else:
                    lower_bound, upper_bound = bounds[dim, 0], bounds[dim, 1]
            else:
                lower_bound, upper_bound = -100, 100

            # 在当前值周围采样
            current_value = current_solution[dim]
            range_size = (upper_bound - lower_bound) * 0.1  # 10%的范围

            sample_values = np.linspace(
                max(lower_bound, current_value - range_size),
                min(upper_bound, current_value + range_size),
                samples_per_dim
            )

            best_value = current_value
            best_dim_fitness = current_fitness

            # 测试每个采样值
            for sample_value in sample_values:
                if self.local_dles.evaluations >= self.local_dles.max_evaluations:
                    break

                # 创建测试解
                test_solution = current_solution.copy()
                test_solution[dim] = sample_value

                # 评估
                test_fitness_array = self.local_dles.evaluate(test_solution)
                if len(test_fitness_array) == 0:
                    break

                test_fitness = test_fitness_array[0]

                # 更新最佳值
                if test_fitness < best_dim_fitness:
                    best_value = sample_value
                    best_dim_fitness = test_fitness

            # 更新当前解
            if best_dim_fitness < current_fitness:
                current_solution[dim] = best_value
                current_fitness = best_dim_fitness

        return current_solution

    def federated_exploitation(self) -> Dict:
        """
        使用全局模型进行联邦开发 - 完整实现DLES第三阶段混合开发策略
        Returns:
            最终优化结果
        """
        print(f"客户端 {self.client_id} 开始联邦开发阶段 (DLES第三阶段)")
        print(f"客户端 {self.client_id} 可用于联邦开发的评估次数: {self.exploitation_evaluations}")

        # 重置评估计数器，为联邦开发阶段分配新的评估次数
        initial_evaluations = self.local_dles.evaluations
        self.local_dles.max_evaluations = initial_evaluations + self.exploitation_evaluations

        if self.global_model is None:
            print(f"客户端 {self.client_id} 没有全局模型，使用本地模型")
            model_to_use = self.local_model
        else:
            model_to_use = self.global_model

        # 步骤1：使用全局模型预测最优个体
        model_to_use.eval()
        with torch.no_grad():
            fitness_tensor = torch.FloatTensor([self.best_fitness]).reshape(-1, 1)
            network_solution = model_to_use(fitness_tensor).numpy().flatten()

        # 检查是否还有评估次数
        if self.local_dles.evaluations >= self.local_dles.max_evaluations:
            print(f"客户端 {self.client_id} 评估次数已用完，跳过联邦开发")
            return {
                'client_id': self.client_id,
                'task_name': self.task_name,
                'final_best_fitness': self.best_fitness,
                'final_evaluations': self.local_dles.evaluations,
                'improvement_from_federation': False
            }

        network_fitness_array = self.local_dles.evaluate(network_solution)
        if len(network_fitness_array) > 0:
            network_fitness = network_fitness_array[0]
            print(f"客户端 {self.client_id} 网络预测解适应度: {network_fitness:.6e}")

            # 更新最佳解
            if network_fitness < self.best_fitness:
                self.best_fitness = network_fitness
                self.best_individual = network_solution.copy()
        else:
            print(f"客户端 {self.client_id} 评估次数已用完，跳过后续优化")
            return {
                'client_id': self.client_id,
                'task_name': self.task_name,
                'final_best_fitness': self.best_fitness,
                'final_evaluations': self.local_dles.evaluations,
                'improvement_from_federation': False
            }

        # 步骤2：CMA-ES优化
        if self.local_dles.evaluations < self.local_dles.max_evaluations:
            print(f"客户端 {self.client_id} 开始CMA-ES优化...")
            cma_solution, cma_evals = self.cma_es_optimization(network_solution)

            cma_fitness_array = self.local_dles.evaluate(cma_solution)
            if len(cma_fitness_array) > 0:
                cma_fitness = cma_fitness_array[0]
                print(f"客户端 {self.client_id} CMA-ES优化完成，适应度: {cma_fitness:.6e}")

                # 更新最佳解
                if cma_fitness < self.best_fitness:
                    self.best_fitness = cma_fitness
                    self.best_individual = cma_solution.copy()

        # 步骤3：单变量采样优化
        if self.local_dles.evaluations < self.local_dles.max_evaluations:
            print(f"客户端 {self.client_id} 开始单变量采样优化...")
            final_solution = self.univariate_sampling(self.best_individual)

            final_fitness_array = self.local_dles.evaluate(final_solution)
            if len(final_fitness_array) > 0:
                final_fitness = final_fitness_array[0]
                print(f"客户端 {self.client_id} 单变量采样优化完成，适应度: {final_fitness:.6e}")

                # 更新最佳解
                if final_fitness < self.best_fitness:
                    self.best_fitness = final_fitness
                    self.best_individual = final_solution.copy()

        print(f"客户端 {self.client_id} 联邦开发完成，最终适应度: {self.best_fitness:.6e}")

        return {
            'client_id': self.client_id,
            'task_name': self.task_name,
            'final_best_fitness': self.best_fitness,
            'final_evaluations': self.local_dles.evaluations,
            'improvement_from_federation': True if self.global_model else False
        }


class FederatedDLESServer:
    """
    联邦DLES服务器
    负责聚合客户端知识，不接触原始数据
    """
    
    def __init__(self, dimension: int = 10):
        """
        初始化服务器
        Args:
            dimension: 问题维度
        """
        self.dimension = dimension
        self.global_model = DLESNetwork(dimension)
        self.clients_info = []
        self.aggregation_history = []

        # 个性化聚合相关
        self.personalized_models = {}  # 存储每个客户端的个性化模型
        self.similarity_matrix = None  # 客户端相似性矩阵
        self.trajectory_data = {}      # 存储客户端轨迹数据
        
    def aggregate_exploration_results(self, client_results: List[Dict]) -> Dict:
        """
        聚合客户端探索结果
        Args:
            client_results: 客户端探索结果列表
        Returns:
            聚合统计信息
        """
        print("服务器开始聚合客户端探索结果...")
        
        self.clients_info = client_results

        # 存储轨迹数据用于相似性计算
        for result in client_results:
            client_id = result['client_id']
            self.trajectory_data[client_id] = result.get('trajectory_data', {})

        # 计算全局统计信息
        all_best_fitness = [result['best_fitness'] for result in client_results]
        total_evaluations = sum(result['evaluations_used'] for result in client_results)
        total_dataset_size = sum(result['dataset_size'] for result in client_results)
        
        global_stats = {
            'total_clients': len(client_results),
            'global_best_fitness': min(all_best_fitness),
            'global_worst_fitness': max(all_best_fitness),
            'average_fitness': np.mean(all_best_fitness),
            'fitness_std': np.std(all_best_fitness),
            'total_evaluations': total_evaluations,
            'total_dataset_size': total_dataset_size,
            'average_dataset_size': total_dataset_size / len(client_results)
        }
        
        print(f"全局探索统计:")
        print(f"  参与客户端数: {global_stats['total_clients']}")
        print(f"  全局最优适应度: {global_stats['global_best_fitness']:.6e}")
        print(f"  平均适应度: {global_stats['average_fitness']:.6e}")
        print(f"  总评估次数: {global_stats['total_evaluations']}")
        
        return global_stats

    def compute_trajectory_similarity_matrix(self) -> np.ndarray:
        """
        计算基于轨迹相似性的客户端相似性矩阵
        使用皮尔逊相关系数衡量改进率序列的相似度
        Returns:
            similarity_matrix: 客户端间相似性矩阵
        """
        print("计算客户端轨迹相似性矩阵...")

        client_ids = list(self.trajectory_data.keys())
        n_clients = len(client_ids)
        similarity_matrix = np.eye(n_clients)  # 初始化为单位矩阵

        for i in range(n_clients):
            for j in range(i + 1, n_clients):
                client_i = client_ids[i]
                client_j = client_ids[j]

                # 获取改进率序列
                rates_i = self.trajectory_data[client_i].get('improvement_rates', [])
                rates_j = self.trajectory_data[client_j].get('improvement_rates', [])

                if len(rates_i) > 1 and len(rates_j) > 1:
                    # 对齐序列长度
                    min_len = min(len(rates_i), len(rates_j))
                    rates_i_aligned = rates_i[:min_len]
                    rates_j_aligned = rates_j[:min_len]

                    # 计算皮尔逊相关系数
                    try:
                        correlation, p_value = pearsonr(rates_i_aligned, rates_j_aligned)
                        # 处理NaN值，设为0相似性
                        if np.isnan(correlation):
                            correlation = 0.0
                        # 将相关系数转换为相似性（取绝对值）
                        similarity = abs(correlation)
                    except:
                        similarity = 0.0
                else:
                    similarity = 0.0

                similarity_matrix[i, j] = similarity
                similarity_matrix[j, i] = similarity

        self.similarity_matrix = similarity_matrix

        print(f"相似性矩阵计算完成，形状: {similarity_matrix.shape}")
        print("相似性矩阵:")
        for i in range(n_clients):
            row_str = " ".join([f"{similarity_matrix[i, j]:.3f}" for j in range(n_clients)])
            print(f"客户端 {client_ids[i]}: [{row_str}]")

        return similarity_matrix

    def personalized_federated_averaging(self, client_models: List[Dict],
                                        similarity_threshold: float = 0.1,
                                        top_k_similar: int = 3) -> Dict[int, Dict]:
        """
        个性化联邦平均聚合算法
        为每个客户端基于相似性选择聚合伙伴，生成个性化全局模型

        Args:
            client_models: 客户端模型参数列表
            similarity_threshold: 相似性阈值，低于此值的客户端不参与聚合
            top_k_similar: 每个客户端最多选择的相似伙伴数量
        Returns:
            personalized_global_models: 每个客户端的个性化全局模型参数字典
        """
        print("开始个性化联邦聚合...")

        if not client_models:
            print("没有客户端模型可聚合")
            return {}

        # 计算轨迹相似性矩阵
        if self.similarity_matrix is None:
            self.compute_trajectory_similarity_matrix()

        client_ids = [model['client_id'] for model in client_models]
        n_clients = len(client_models)
        personalized_models = {}

        # 创建客户端ID到索引的映射
        client_id_to_matrix_idx = {cid: idx for idx, cid in enumerate(self.trajectory_data.keys())}

        # 为每个客户端单独计算个性化聚合
        for i, target_client_model in enumerate(client_models):
            target_client_id = target_client_model['client_id']
            print(f"\n为客户端 {target_client_id} 计算个性化聚合...")

            # 获取与目标客户端的相似性（使用相似性矩阵中的索引）
            matrix_idx = client_id_to_matrix_idx.get(target_client_id, i)
            similarities = self.similarity_matrix[matrix_idx, :]

            # 选择相似的客户端（包括自己）
            similar_indices = []
            similar_weights = []

            # 添加自己（权重为1.0）
            similar_indices.append(i)
            similar_weights.append(1.0)

            # 选择最相似的其他客户端（只考虑参与训练的客户端）
            other_similarities = []
            for j, other_client_model in enumerate(client_models):
                if j != i:  # 不包括自己
                    other_client_id = other_client_model['client_id']
                    other_matrix_idx = client_id_to_matrix_idx.get(other_client_id, j)
                    if other_matrix_idx < len(similarities):
                        sim = similarities[other_matrix_idx]
                        if sim >= similarity_threshold:
                            other_similarities.append((j, sim))

            other_similarities.sort(key=lambda x: x[1], reverse=True)

            # 取前top_k_similar个相似客户端
            for j, sim in other_similarities[:top_k_similar]:
                similar_indices.append(j)
                similar_weights.append(sim)

            print(f"客户端 {target_client_id} 选择的聚合伙伴: {[client_ids[idx] for idx in similar_indices]}")
            print(f"对应的相似性权重: {[f'{w:.3f}' for w in similar_weights]}")

            # 计算加权平均（结合数据集大小和相似性）
            total_weight = 0.0
            personalized_state_dict = {}

            # 初始化参数字典
            for key in client_models[0]['model_state_dict'].keys():
                personalized_state_dict[key] = torch.zeros_like(
                    client_models[0]['model_state_dict'][key]
                )

            # 加权聚合
            for idx, similarity_weight in zip(similar_indices, similar_weights):
                model = client_models[idx]
                dataset_weight = model['dataset_size']
                combined_weight = similarity_weight * dataset_weight
                total_weight += combined_weight

                for key in personalized_state_dict.keys():
                    personalized_state_dict[key] += combined_weight * model['model_state_dict'][key]

            # 归一化
            if total_weight > 0:
                for key in personalized_state_dict.keys():
                    personalized_state_dict[key] /= total_weight

            personalized_models[target_client_id] = personalized_state_dict

            print(f"客户端 {target_client_id} 个性化模型聚合完成")

        self.personalized_models = personalized_models

        # 记录聚合历史
        self.aggregation_history.append({
            'round': len(self.aggregation_history) + 1,
            'aggregation_type': 'personalized',
            'participating_clients': len(client_models),
            'similarity_threshold': similarity_threshold,
            'top_k_similar': top_k_similar
        })

        print(f"个性化联邦聚合完成，为 {len(personalized_models)} 个客户端生成了个性化模型")

        return personalized_models
    
    def federated_averaging(self, client_models: List[Dict]) -> Dict:
        """
        联邦平均聚合算法
        Args:
            client_models: 客户端模型参数列表
        Returns:
            全局模型参数
        """
        print("服务器开始联邦平均聚合...")
        
        if not client_models:
            print("没有客户端模型可聚合")
            return self.global_model.state_dict()
        
        # 计算权重（基于数据集大小）
        total_samples = sum(model['dataset_size'] for model in client_models)
        weights = [model['dataset_size'] / total_samples for model in client_models]
        
        # 初始化全局模型参数
        global_state_dict = {}
        
        # 对每个参数进行加权平均
        for key in client_models[0]['model_state_dict'].keys():
            global_state_dict[key] = torch.zeros_like(
                client_models[0]['model_state_dict'][key]
            )
            
            for i, model in enumerate(client_models):
                global_state_dict[key] += weights[i] * model['model_state_dict'][key]
        
        # 更新全局模型
        self.global_model.load_state_dict(global_state_dict)
        
        print(f"联邦聚合完成，参与客户端数: {len(client_models)}")
        
        # 记录聚合历史
        self.aggregation_history.append({
            'round': len(self.aggregation_history) + 1,
            'participating_clients': len(client_models),
            'client_weights': weights
        })
        
        return global_state_dict
    
    def get_global_model_state(self) -> Dict:
        """获取全局模型状态"""
        return self.global_model.state_dict()
    
    def aggregate_final_results(self, client_results: List[Dict]) -> Dict:
        """
        聚合最终结果
        Args:
            client_results: 客户端最终结果
        Returns:
            全局最终统计
        """
        print("服务器聚合最终结果...")
        
        final_fitness = [result['final_best_fitness'] for result in client_results]
        total_evaluations = sum(result['final_evaluations'] for result in client_results)
        improved_clients = sum(1 for result in client_results 
                             if result.get('improvement_from_federation', False))
        
        final_stats = {
            'total_clients': len(client_results),
            'global_best_fitness': min(final_fitness),
            'global_worst_fitness': max(final_fitness),
            'average_final_fitness': np.mean(final_fitness),
            'final_fitness_std': np.std(final_fitness),
            'total_final_evaluations': total_evaluations,
            'clients_improved_by_federation': improved_clients,
            'federation_improvement_rate': improved_clients / len(client_results)
        }
        
        return final_stats
