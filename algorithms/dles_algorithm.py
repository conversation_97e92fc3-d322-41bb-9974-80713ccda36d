import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.stats import norm
from pyDOE import lhs
import warnings
warnings.filterwarnings('ignore')


class DLESNetwork(nn.Module):
    """
    深度学习增强搜索路径重构神经网络
    网络结构: 1-2D-4D-D (Net3结构)
    """
    def __init__(self, dimension):
        super(DLESNetwork, self).__init__()
        self.dimension = dimension
        
        # 网络层定义
        self.input_layer = nn.Linear(1, 2 * dimension)
        self.hidden_layer1 = nn.Linear(2 * dimension, 4 * dimension)
        self.output_layer = nn.Linear(4 * dimension, dimension)
        
        # 激活函数
        self.tanh = nn.Tanh()
        
    def forward(self, fitness):
        """
        前向传播
        Args:
            fitness: 适应度值 (batch_size, 1)
        Returns:
            predicted_individual: 预测的个体 (batch_size, dimension)
        """
        x = self.input_layer(fitness)
        x = self.tanh(self.hidden_layer1(x))
        x = self.output_layer(x)
        return x


class UMDAc:
    """
    优化的单变量边缘分布算法连续版本 (UMDAc)
    """
    def __init__(self, dimension, population_size):
        self.dimension = dimension
        self.population_size = population_size
        
    def generate_offspring(self, population, fitness_values):
        """
        生成后代个体
        Args:
            population: 当前种群 (N, D)
            fitness_values: 适应度值 (N,)
        Returns:
            offspring: 后代种群 (N, D)
        """
        N = len(population)
        
        # 计算权重
        weights_prime = np.array([np.log(N + 0.5) - np.log(i + 1) for i in range(N)])
        weights = weights_prime / np.sum(weights_prime)
        
        # 计算加权均值和标准差
        mu = np.zeros(self.dimension)
        for d in range(self.dimension):
            mu[d] = np.sum(weights * population[:, d])
        
        # 计算标准差
        sigma = np.zeros(self.dimension)
        for d in range(self.dimension):
            sigma[d] = np.sqrt(np.sum(weights * (population[:, d] - mu[d])**2))
            sigma[d] = max(sigma[d], 1e-8)  # 避免标准差为0
        
        # 生成后代
        offspring = np.random.normal(mu, sigma, (N, self.dimension))
        
        return offspring


def fitness_mse_loss(predictions, targets, fitness_values):
    """
    适应度均方误差损失函数 (FMSE)
    Args:
        predictions: 网络预测值 (batch_size, dimension)
        targets: 真实值 (batch_size, dimension)
        fitness_values: 适应度值 (batch_size,)
    Returns:
        loss: FMSE损失值
    """
    mse = torch.mean((predictions - targets)**2, dim=1)  # 每个样本的MSE
    fmse = torch.mean(mse + fitness_values)  # 加入适应度权重
    return fmse


class DLES:
    """
    深度学习增强搜索路径重构进化策略算法 (DLES)
    """
    def __init__(self, objective_function, dimension, population_size=50, 
                 max_evaluations=10000, exploration_iterations=100,
                 learning_rate=0.5, epochs=10):
        """
        初始化DLES算法
        Args:
            objective_function: 目标函数
            dimension: 问题维度
            population_size: 种群大小
            max_evaluations: 最大评估次数
            exploration_iterations: 探索迭代次数
            learning_rate: 学习率
            epochs: 网络训练轮数
        """
        self.objective_function = objective_function
        self.dimension = dimension
        self.population_size = population_size
        self.max_evaluations = max_evaluations
        self.exploration_iterations = exploration_iterations
        self.learning_rate = learning_rate
        self.epochs = epochs
        
        # 算法状态
        self.evaluations = 0
        self.best_fitness = float('inf')
        self.best_individual = None
        self.dataset = []  # 存储(individual, fitness)对
        
        # 初始化组件
        self.umda = UMDAc(dimension, population_size)
        self.network = DLESNetwork(dimension)
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
    def evaluate(self, individuals):
        """
        评估个体适应度
        Args:
            individuals: 个体数组 (N, D) 或 (D,)
        Returns:
            fitness_values: 适应度值
        """
        if individuals.ndim == 1:
            individuals = individuals.reshape(1, -1)

        fitness_values = []
        for individual in individuals:
            if self.evaluations >= self.max_evaluations:
                # 如果评估次数用完，直接停止评估
                break
            else:
                fitness = self.objective_function(individual)
                fitness_values.append(fitness)
                self.evaluations += 1

        return np.array(fitness_values)

    def exploration_sampling(self):
        """
        探索采样阶段 - 算法1
        使用UMDAc进行搜索空间探索并收集数据集
        """
        # 初始化种群
        if hasattr(self.objective_function, 'x_bound'):
            bounds = self.objective_function.x_bound
            if isinstance(bounds, list) and len(bounds) == 2:
                # 统一边界
                population = np.random.uniform(bounds[0], bounds[1],
                                             (self.population_size, self.dimension))
            else:
                # 每维不同边界
                population = np.random.uniform(bounds[:, 0], bounds[:, 1],
                                             (self.population_size, self.dimension))
        else:
            # 默认边界 [-100, 100]
            population = np.random.uniform(-100, 100,
                                         (self.population_size, self.dimension))

        # 评估初始种群
        fitness_values = self.evaluate(population)

        # 找到最佳个体
        best_idx = np.argmin(fitness_values)
        self.best_fitness = fitness_values[best_idx]
        self.best_individual = population[best_idx].copy()

        # 添加到数据集
        self.dataset.append((self.best_individual.copy(), self.best_fitness))

        # 迭代探索
        for t in range(self.exploration_iterations):
            if self.evaluations >= self.max_evaluations:
                break

            # 排序种群
            sorted_indices = np.argsort(fitness_values)
            population = population[sorted_indices]
            fitness_values = fitness_values[sorted_indices]

            # 生成后代
            offspring = self.umda.generate_offspring(population, fitness_values)

            # 评估后代
            offspring_fitness = self.evaluate(offspring)

            # 如果评估次数用完，直接停止
            if len(offspring_fitness) == 0:
                break

            # 只使用实际评估的后代
            actual_offspring = offspring[:len(offspring_fitness)]

            # 合并种群
            combined_population = np.vstack([population, actual_offspring])
            combined_fitness = np.hstack([fitness_values, offspring_fitness])

            # 选择下一代
            sorted_indices = np.argsort(combined_fitness)
            population = combined_population[sorted_indices[:self.population_size]]
            fitness_values = combined_fitness[sorted_indices[:self.population_size]]

            # 检查是否找到更好的解
            current_best_fitness = fitness_values[0]
            if current_best_fitness < self.best_fitness:
                self.best_fitness = current_best_fitness
                self.best_individual = population[0].copy()
                # 只在找到更好解时添加到数据集
                self.dataset.append((self.best_individual.copy(), self.best_fitness))

        print(f"探索阶段完成，数据集大小: {len(self.dataset)}, 最佳适应度: {self.best_fitness:.6f}")

    def network_reconstruction(self):
        """
        网络重构阶段 - 算法2
        训练神经网络重构搜索路径
        """
        if len(self.dataset) < 2:
            print("数据集太小，跳过网络训练")
            return

        # 准备训练数据
        individuals = np.array([item[0] for item in self.dataset])
        fitness_values = np.array([item[1] for item in self.dataset])

        # 转换为PyTorch张量
        X_fitness = torch.FloatTensor(fitness_values).reshape(-1, 1)
        y_individuals = torch.FloatTensor(individuals)

        # 训练网络
        self.network.train()
        for epoch in range(self.epochs):
            self.optimizer.zero_grad()

            # 前向传播
            predictions = self.network(X_fitness)

            # 计算FMSE损失
            loss = fitness_mse_loss(predictions, y_individuals,
                                  torch.FloatTensor(fitness_values))

            # 反向传播
            loss.backward()
            self.optimizer.step()

            if epoch % (self.epochs // 2) == 0:
                print(f"训练轮次 {epoch}, 损失: {loss.item():.6f}")

        print(f"网络训练完成，最终损失: {loss.item():.6f}")

    def predict_individual(self, fitness_value):
        """
        使用训练好的网络预测个体
        Args:
            fitness_value: 适应度值
        Returns:
            predicted_individual: 预测的个体
        """
        self.network.eval()
        with torch.no_grad():
            fitness_tensor = torch.FloatTensor([fitness_value]).reshape(-1, 1)
            predicted = self.network(fitness_tensor)
            return predicted.numpy().flatten()

    def cma_es_optimization(self, initial_solution, max_iterations=100):
        """
        简化的CMA-ES优化
        Args:
            initial_solution: 初始解
            max_iterations: 最大迭代次数
        Returns:
            optimized_solution: 优化后的解
            evaluations_used: 使用的评估次数
        """
        current_solution = initial_solution.copy()
        current_fitness_array = self.evaluate(current_solution)

        if len(current_fitness_array) == 0:
            return initial_solution, 0

        current_fitness = current_fitness_array[0]

        # CMA-ES参数
        sigma = 1.0  # 步长
        population_size = 4 + int(3 * np.log(self.dimension))
        mu = population_size // 2

        evaluations_used = 1
        no_improvement_count = 0

        for iteration in range(max_iterations):
            if self.evaluations >= self.max_evaluations:
                break

            # 生成候选解
            candidates = []
            for _ in range(population_size):
                candidate = current_solution + sigma * np.random.randn(self.dimension)
                candidates.append(candidate)

            # 评估候选解
            candidate_fitness = self.evaluate(np.array(candidates))
            evaluations_used += len(candidate_fitness)

            # 如果没有评估结果，停止优化
            if len(candidate_fitness) == 0:
                break

            # 选择最佳解
            best_idx = np.argmin(candidate_fitness)
            if candidate_fitness[best_idx] < current_fitness:
                current_solution = candidates[best_idx].copy()
                current_fitness = candidate_fitness[best_idx]
                no_improvement_count = 0
            else:
                no_improvement_count += 1

            # 如果连续多次无改进，则停止
            if no_improvement_count >= 10:
                break

            # 调整步长
            if no_improvement_count > 5:
                sigma *= 0.9
            else:
                sigma *= 1.1

        return current_solution, evaluations_used

    def univariate_sampling(self, initial_solution, max_iterations=100):
        """
        单变量采样优化
        Args:
            initial_solution: 初始解
            max_iterations: 最大迭代次数
        Returns:
            optimized_solution: 优化后的解
        """
        # 生成初始种群
        population_size = 2 * self.population_size
        population = []

        for _ in range(population_size):
            individual = initial_solution + np.random.normal(0, 1, self.dimension)
            population.append(individual)

        population = np.array(population)
        fitness_values = self.evaluate(population)

        # 如果没有评估结果，返回初始解
        if len(fitness_values) == 0:
            return initial_solution

        # 只保留实际评估的个体
        population = population[:len(fitness_values)]

        # 控制参数
        cc = 0.99  # 收敛控制参数
        no_improvement_count = 0

        for iteration in range(max_iterations):
            if self.evaluations >= self.max_evaluations:
                break

            # 排序并选择最佳个体
            sorted_indices = np.argsort(fitness_values)
            population = population[sorted_indices]
            fitness_values = fitness_values[sorted_indices]

            # 选择前N个个体
            selected_population = population[:self.population_size]

            # 计算每维的均值和标准差
            mu = np.mean(selected_population, axis=0)
            sigma = np.std(selected_population, axis=0)
            sigma = np.maximum(sigma, 1e-8)  # 避免标准差为0

            # 检查是否有改进
            current_best = fitness_values[0]
            if iteration > 0 and current_best >= previous_best:
                no_improvement_count += 1
                # 减小搜索范围
                sigma *= cc
            else:
                no_improvement_count = 0

            previous_best = current_best

            # 如果连续无改进，停止
            if no_improvement_count >= 10:
                break

            # 生成新的候选解
            new_population = []
            for _ in range(population_size):
                individual = np.random.normal(mu, sigma)
                new_population.append(individual)

            population = np.array(new_population)
            fitness_values = self.evaluate(population)

            # 如果没有评估结果，停止优化
            if len(fitness_values) == 0:
                break

            # 只保留实际评估的个体
            population = population[:len(fitness_values)]

        # 返回最佳解
        best_idx = np.argmin(fitness_values)
        return population[best_idx]

    def hybrid_exploitation(self):
        """
        混合开发阶段 - 算法3
        结合CMA-ES和单变量采样进行最终优化
        """
        if len(self.dataset) == 0:
            print("没有数据集，跳过混合开发阶段")
            return

        # 使用网络预测最优个体
        network_solution = self.predict_individual(self.best_fitness)

        network_fitness_array = self.evaluate(network_solution)
        if len(network_fitness_array) > 0:
            print(f"网络预测解的适应度: {network_fitness_array[0]:.6f}")
        else:
            print("评估次数已用完，跳过网络预测解评估")
            return

        # 第一阶段：CMA-ES优化
        print("开始CMA-ES优化...")
        cma_solution, _ = self.cma_es_optimization(network_solution)
        cma_fitness_array = self.evaluate(cma_solution)

        if len(cma_fitness_array) > 0:
            cma_fitness = cma_fitness_array[0]
            print(f"CMA-ES优化完成，适应度: {cma_fitness:.6f}")

            # 更新最佳解
            if cma_fitness < self.best_fitness:
                self.best_fitness = cma_fitness
                self.best_individual = cma_solution.copy()
        else:
            print("评估次数已用完，跳过CMA-ES优化")
            return

        # 第二阶段：单变量采样优化
        if self.evaluations < self.max_evaluations:
            print("开始单变量采样优化...")
            final_solution = self.univariate_sampling(cma_solution)
            final_fitness_array = self.evaluate(final_solution)

            if len(final_fitness_array) > 0:
                final_fitness = final_fitness_array[0]
                print(f"单变量采样优化完成，适应度: {final_fitness:.6f}")

                # 更新最佳解
                if final_fitness < self.best_fitness:
                    self.best_fitness = final_fitness
                    self.best_individual = final_solution.copy()
            else:
                print("评估次数已用完，跳过单变量采样优化")

    def optimize(self):
        """
        DLES主优化流程
        Returns:
            best_individual: 最优个体
            best_fitness: 最优适应度值
            evaluations: 使用的评估次数
        """
        print("=" * 50)
        print("开始DLES优化")
        print(f"问题维度: {self.dimension}")
        print(f"种群大小: {self.population_size}")
        print(f"最大评估次数: {self.max_evaluations}")
        print("=" * 50)

        # 阶段1：探索采样
        print("\n阶段1: 探索采样")
        self.exploration_sampling()

        # 阶段2：网络重构
        print("\n阶段2: 网络重构")
        self.network_reconstruction()

        # 阶段3：混合开发
        print("\n阶段3: 混合开发")
        self.hybrid_exploitation()

        print("\n" + "=" * 50)
        print("DLES优化完成")
        print(f"最优适应度: {self.best_fitness:.6f}")
        print(f"使用评估次数: {self.evaluations}")
        print("=" * 50)

        return self.best_individual, self.best_fitness, self.evaluations


# 辅助函数
def run_dles_on_function(objective_function, dimension, max_evaluations=None,
                        population_size=50, runs=1):
    """
    在指定函数上运行DLES算法
    Args:
        objective_function: 目标函数
        dimension: 问题维度
        max_evaluations: 最大评估次数
        population_size: 种群大小
        runs: 运行次数
    Returns:
        results: 运行结果列表
    """
    if max_evaluations is None:
        max_evaluations = 10000 * dimension

    results = []

    for run in range(runs):
        print(f"\n运行 {run + 1}/{runs}")

        # 创建DLES实例
        dles = DLES(
            objective_function=objective_function,
            dimension=dimension,
            population_size=population_size,
            max_evaluations=max_evaluations,
            exploration_iterations=100,
            learning_rate=0.5,
            epochs=10
        )

        # 运行优化
        best_individual, best_fitness, evaluations = dles.optimize()

        results.append({
            'best_individual': best_individual,
            'best_fitness': best_fitness,
            'evaluations': evaluations,
            'run': run + 1
        })

    return results
